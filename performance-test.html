<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单性能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: bold;
        }
        .test-button {
            background: #409EFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #337ecc;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
        }
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .metric {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #409EFF;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        .recommendations h3 {
            margin-top: 0;
            color: #856404;
        }
        .recommendations ul {
            margin-bottom: 0;
        }
        .recommendations li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>菜单性能优化测试工具</h1>
        
        <div class="test-section">
            <div class="test-title">🚀 页面加载性能测试</div>
            <button class="test-button" onclick="testPageLoad()">测试页面加载时间</button>
            <button class="test-button" onclick="testMenuRender()">测试菜单渲染时间</button>
            <button class="test-button" onclick="testDataLoad()">测试数据加载时间</button>
            <div id="loadResult" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <div class="test-title">📊 实时性能指标</div>
            <button class="test-button" onclick="updateMetrics()">刷新指标</button>
            <div class="performance-metrics">
                <div class="metric">
                    <div class="metric-value" id="domContentLoaded">-</div>
                    <div class="metric-label">DOM 加载时间 (ms)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="pageLoad">-</div>
                    <div class="metric-label">页面完全加载 (ms)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="firstPaint">-</div>
                    <div class="metric-label">首次绘制 (ms)</div>
                </div>
                <div class="metric">
                    <div class="metric-value" id="firstContentfulPaint">-</div>
                    <div class="metric-label">首次内容绘制 (ms)</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🔧 菜单交互测试</div>
            <button class="test-button" onclick="testMenuHover()">测试菜单悬停响应</button>
            <button class="test-button" onclick="testMenuClick()">测试菜单点击响应</button>
            <button class="test-button" onclick="testDropdownOpen()">测试下拉菜单展开</button>
            <div id="interactionResult" class="result" style="display:none;"></div>
        </div>

        <div class="recommendations">
            <h3>🎯 优化建议</h3>
            <ul>
                <li><strong>延迟加载：</strong>将非关键数据的加载推迟到菜单渲染完成后</li>
                <li><strong>预加载：</strong>使用 link rel="prefetch" 预加载常用的数据文件</li>
                <li><strong>防抖处理：</strong>对菜单选择事件进行防抖，避免频繁触发</li>
                <li><strong>分批加载：</strong>将大量数据分批加载，避免阻塞UI线程</li>
                <li><strong>缓存策略：</strong>对已加载的数据进行缓存，避免重复请求</li>
                <li><strong>CSS优化：</strong>使用 will-change 和 transform3d 优化动画性能</li>
                <li><strong>并行加载：</strong>使用 Promise.all 并行加载多个数据源</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载性能测试
        function testPageLoad() {
            const result = document.getElementById('loadResult');
            result.style.display = 'block';
            
            const startTime = performance.now();
            
            // 模拟页面加载
            setTimeout(() => {
                const endTime = performance.now();
                const loadTime = endTime - startTime;
                
                result.innerHTML = `
                    <strong>页面加载测试结果：</strong><br>
                    模拟加载时间: ${loadTime.toFixed(2)} ms<br>
                    建议: ${loadTime > 100 ? '⚠️ 加载时间较长，建议优化' : '✅ 加载时间良好'}
                `;
            }, Math.random() * 200 + 50);
        }

        // 菜单渲染测试
        function testMenuRender() {
            const result = document.getElementById('loadResult');
            result.style.display = 'block';
            
            const startTime = performance.now();
            
            // 模拟菜单渲染
            const menuItems = 20; // 假设有20个菜单项
            let rendered = 0;
            
            const renderInterval = setInterval(() => {
                rendered++;
                if (rendered >= menuItems) {
                    clearInterval(renderInterval);
                    const endTime = performance.now();
                    const renderTime = endTime - startTime;
                    
                    result.innerHTML = `
                        <strong>菜单渲染测试结果：</strong><br>
                        渲染时间: ${renderTime.toFixed(2)} ms<br>
                        菜单项数量: ${menuItems}<br>
                        平均每项: ${(renderTime / menuItems).toFixed(2)} ms<br>
                        建议: ${renderTime > 50 ? '⚠️ 渲染时间较长，建议优化' : '✅ 渲染性能良好'}
                    `;
                }
            }, 2);
        }

        // 数据加载测试
        function testDataLoad() {
            const result = document.getElementById('loadResult');
            result.style.display = 'block';
            
            const startTime = performance.now();
            
            // 模拟数据加载
            Promise.all([
                new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50)),
                new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50)),
                new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50)),
                new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50)),
                new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50))
            ]).then(() => {
                const endTime = performance.now();
                const loadTime = endTime - startTime;
                
                result.innerHTML = `
                    <strong>数据加载测试结果：</strong><br>
                    总加载时间: ${loadTime.toFixed(2)} ms<br>
                    数据源数量: 5<br>
                    建议: ${loadTime > 200 ? '⚠️ 数据加载较慢，建议分批或延迟加载' : '✅ 数据加载性能良好'}
                `;
            });
        }

        // 更新性能指标
        function updateMetrics() {
            if (performance.timing) {
                const timing = performance.timing;
                const navigation = timing.navigationStart;
                
                document.getElementById('domContentLoaded').textContent = 
                    (timing.domContentLoadedEventEnd - navigation).toFixed(0);
                document.getElementById('pageLoad').textContent = 
                    (timing.loadEventEnd - navigation).toFixed(0);
            }
            
            if (performance.getEntriesByType) {
                const paintEntries = performance.getEntriesByType('paint');
                paintEntries.forEach(entry => {
                    if (entry.name === 'first-paint') {
                        document.getElementById('firstPaint').textContent = entry.startTime.toFixed(0);
                    }
                    if (entry.name === 'first-contentful-paint') {
                        document.getElementById('firstContentfulPaint').textContent = entry.startTime.toFixed(0);
                    }
                });
            }
        }

        // 菜单交互测试
        function testMenuHover() {
            const result = document.getElementById('interactionResult');
            result.style.display = 'block';
            
            const startTime = performance.now();
            
            setTimeout(() => {
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                
                result.innerHTML = `
                    <strong>菜单悬停测试结果：</strong><br>
                    响应时间: ${responseTime.toFixed(2)} ms<br>
                    建议: ${responseTime > 16 ? '⚠️ 响应时间超过一帧(16ms)，可能影响流畅度' : '✅ 响应时间良好'}
                `;
            }, Math.random() * 30 + 5);
        }

        function testMenuClick() {
            const result = document.getElementById('interactionResult');
            result.style.display = 'block';
            
            const startTime = performance.now();
            
            setTimeout(() => {
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                
                result.innerHTML = `
                    <strong>菜单点击测试结果：</strong><br>
                    响应时间: ${responseTime.toFixed(2)} ms<br>
                    建议: ${responseTime > 100 ? '⚠️ 点击响应较慢，建议优化事件处理' : '✅ 点击响应良好'}
                `;
            }, Math.random() * 150 + 20);
        }

        function testDropdownOpen() {
            const result = document.getElementById('interactionResult');
            result.style.display = 'block';
            
            const startTime = performance.now();
            
            setTimeout(() => {
                const endTime = performance.now();
                const responseTime = endTime - startTime;
                
                result.innerHTML = `
                    <strong>下拉菜单展开测试结果：</strong><br>
                    展开时间: ${responseTime.toFixed(2)} ms<br>
                    建议: ${responseTime > 200 ? '⚠️ 下拉菜单展开较慢，建议优化动画或减少DOM操作' : '✅ 下拉菜单性能良好'}
                `;
            }, Math.random() * 250 + 50);
        }

        // 页面加载完成后自动更新指标
        window.addEventListener('load', () => {
            setTimeout(updateMetrics, 100);
        });
    </script>
</body>
</html>
