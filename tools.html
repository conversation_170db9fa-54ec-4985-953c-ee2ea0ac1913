<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="https://img.scmttec.com/gw/laiyuemiao/<EMAIL>">
    <title>约苗小工具</title>
    <!-- 关键CSS优先加载 -->
    <link rel="stylesheet" href="./lib/css/tool.css">
    <link rel="stylesheet" href="https://img.scmttec.com/hospital/libs/element-ui2.15.6/theme-chalk/index.min.css">

    <!-- 预加载关键资源 -->
    <link rel="preload" href="./lib/js/vue.min.js" as="script">
    <link rel="preload" href="./lib/js/axios.min.js" as="script">
    <link rel="preload" href="https://img.scmttec.com/hospital/libs/element-ui.min.js" as="script">

    <!-- 预加载关键数据文件 -->
    <link rel="prefetch" href="./data/platformUrl.json">
    <link rel="prefetch" href="./data/ai.json">
    <link rel="prefetch" href="./data/security.json">
    <link rel="prefetch" href="./data/testPlatform.json">
    <link rel="prefetch" href="./data/apkUrl.json">

    <!-- 核心JS库 -->
    <script src="./lib/js/vue.min.js"></script>
    <script src="./lib/js/axios.min.js"></script>
    <script src="https://img.scmttec.com/hospital/libs/element-ui.min.js"></script>

    <!-- 预加载 Base64 库，避免使用时未定义的问题 -->
    <script src="./lib/js/js-base64-main/base64.js"></script>

    <!-- Base64 备用方案和初始化检查 -->
    <script>
        // 确保 Base64 库正确加载，提供备用方案
        window.addEventListener('DOMContentLoaded', function() {
            // 检查 Base64 是否正确加载
            if (typeof Base64 === 'undefined' || typeof Base64.encode !== 'function') {
                console.warn('Base64 库未正确加载，使用浏览器原生方法作为备用方案');

                // 提供基于浏览器原生 btoa/atob 的备用实现
                window.Base64 = {
                    encode: function(str) {
                        try {
                            // 处理中文字符
                            return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, function(match, p1) {
                                return String.fromCharCode('0x' + p1);
                            }));
                        } catch (e) {
                            throw new Error('Base64 编码失败: ' + e.message);
                        }
                    },
                    decode: function(str) {
                        try {
                            // 处理中文字符
                            return decodeURIComponent(atob(str).split('').map(function(c) {
                                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                            }).join(''));
                        } catch (e) {
                            throw new Error('Base64 解码失败: ' + e.message);
                        }
                    }
                };
            }

            // 验证 Base64 功能
            try {
                var testStr = 'Hello 测试';
                var encoded = Base64.encode(testStr);
                var decoded = Base64.decode(encoded);
                if (decoded !== testStr) {
                    throw new Error('Base64 编解码验证失败');
                }
                console.log('✅ Base64 库验证成功');
            } catch (e) {
                console.error('❌ Base64 库验证失败:', e.message);
            }
        });
    </script>

</head>
<body>
    <el-row id="app" v-cloak>
        <el-container class="operation-wrapper">
            <el-header>
                <div class="header-content">
                    <div class="header-left">
                        <img class="header-img" :src="logo" alt="some_text">
                        <h2>测试小工具</h2>
                    </div>
                    <el-menu
                        :default-active="activeMenu"
                        class="header-menu"
                        mode="horizontal"
                        @select="handleMenuSelect"
                        background-color="transparent"
                        text-color="#fff"
                        active-text-color="#fff">
                        <!-- 核心功能组 -->
                        <el-menu-item index="workbench">
                            <i class="el-icon-s-platform"></i>工作台
                        </el-menu-item>

                        <!-- 加密解密工具组 -->
                        <el-submenu index="crypto">
                            <template slot="title">
                                <i class="el-icon-lock"></i>加密解密工具
                            </template>
                            <el-menu-item index="decrypt">
                                <i class="el-icon-key"></i>埋点解密
                            </el-menu-item>
                            <el-menu-item index="base64">
                                <i class="el-icon-key"></i>Base64加解密
                            </el-menu-item>
                            <el-menu-item index="sm4">
                                <i class="el-icon-lock"></i>SM4加解密
                            </el-menu-item>
                            <el-menu-item index="aes">
                                <i class="el-icon-key"></i>AES加密解析
                            </el-menu-item>
                        </el-submenu>

                        <!-- 数据处理工具组 -->
                        <el-submenu index="data">
                            <template slot="title">
                                <i class="el-icon-data-analysis"></i>数据处理工具
                            </template>
                            <el-menu-item index="doris">
                                <i class="el-icon-data-analysis"></i>Doris
                            </el-menu-item>
                            <el-menu-item index="json">
                                <i class="el-icon-document"></i>Json格式化
                            </el-menu-item>
                            <el-menu-item index="url">
                                <i class="el-icon-link"></i>URL编码解码
                            </el-menu-item>
                            <el-menu-item index="timestamp">
                                <i class="el-icon-time"></i>时间戳转换
                            </el-menu-item>
                        </el-submenu>

                        <!-- 实用工具组 -->
                        <el-submenu index="utils">
                            <template slot="title">
                                <i class="el-icon-s-tools"></i>实用工具
                            </template>
                            <el-menu-item index="image">
                                <i class="el-icon-picture"></i>图片生成
                            </el-menu-item>
                            <el-menu-item index="idcard">
                                <i class="el-icon-document"></i>身份证生成
                            </el-menu-item>
                            <el-menu-item index="location">
                                <i class="el-icon-location"></i>经纬度查询
                            </el-menu-item>
                            <el-menu-item index="cron">
                                <i class="el-icon-timer"></i>Cron表达式
                            </el-menu-item>
                        </el-submenu>

                        <!-- 更新日志 -->
                        <el-menu-item index="logs">
                            <i class="el-icon-notebook-2"></i>更新日志
                        </el-menu-item>
                    </el-menu>
                </div>
            </el-header>
            <el-main>
                <div class="main-container">
                    <!-- 工作台 -->
                    <div v-show="activeMenu === 'workbench'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-s-platform"></i> 工作台</h2>
                        </div>
                        <div class="section-content">
                            <!-- AI工具 -->
                            <div class="tool-section">
                                <div class="section-title">
                                    <i class="el-icon-cpu"></i>
                                    <span>AI工具</span>
                                </div>
                                <div class="card-grid">
                                    <el-card v-for="item in aiData" :key="item.platformName" class="tool-card" shadow="hover">
                                        <div class="tool-card-content">
                                            <img :src="item.imageUrl" class="tool-icon" alt="tool icon">
                                            <el-link type="primary" :underline="false" :href="item.url" target="_blank">{{item.platformName}}</el-link>
                                            <p v-show="item.isShow" class="tool-description">{{item.remark}}</p>
                                        </div>
                                    </el-card>
                                </div>
                            </div>

                            <!-- 安全测试 -->
                            <div class="tool-section">
                                <div class="section-title">
                                    <i class="el-icon-lock"></i>
                                    <span>安全测试-练习靶场</span>
                                </div>
                                <div class="card-grid">
                                    <el-card v-for="item in securityData" :key="item.platformName" class="tool-card" shadow="hover">
                                        <div class="tool-card-content">
                                            <el-link type="primary" :underline="false" :href="item.url" target="_blank">{{item.platformName}}</el-link>
                                            <p v-show="item.isShow" class="tool-description">{{item.account}}</p>
                                        </div>
                                    </el-card>
                                </div>
                            </div>

                            <!-- 测试平台 -->
                            <div class="tool-section">
                                <div class="section-title">
                                    <i class="el-icon-s-tools"></i>
                                    <span>测试平台</span>
                                </div>
                                <div class="card-grid">
                                    <el-card v-for="item in testPlatfromData" :key="item.platformName" class="tool-card" shadow="hover">
                                        <div class="tool-card-content">
                                            <el-link type="primary" :underline="false" :href="item.url" target="_blank">{{item.platformName}}</el-link>
                                            <p v-show="item.isShow" class="tool-description">{{item.account}}</p>
                                        </div>
                                    </el-card>
                                </div>
                            </div>

                            <!-- 平台链接 -->
                            <div class="tool-section">
                                <div class="section-title">
                                    <i class="el-icon-link"></i>
                                    <span>平台链接</span>
                                </div>
                                <el-card class="table-card" shadow="hover">
                                    <el-table :data="tableData" style="width: 100%">
                                        <el-table-column prop="platform" label="平台名称" width="180"></el-table-column>
                                        <el-table-column prop="test_url" label="测试">
                                            <template slot-scope="scope">
                                                <div class="link-wrapper">
                                                    <el-link type="primary" :underline="false" :href="scope.row.test_url" target="_blank">{{ scope.row.test_url }}</el-link>
                                                    <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.test_url)">复制链接</el-button>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="pre_url" label="预发">
                                            <template slot-scope="scope">
                                                <div class="link-wrapper">
                                                    <el-link type="primary" :underline="false" :href="scope.row.pre_url" target="_blank">{{ scope.row.pre_url }}</el-link>
                                                    <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.pre_url)">复制链接</el-button>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="prod_url" label="生产">
                                            <template slot-scope="scope">
                                                <div class="link-wrapper">
                                                    <el-link type="primary" :underline="false" :href="scope.row.prod_url" target="_blank">{{ scope.row.prod_url }}</el-link>
                                                    <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.prod_url)">复制链接</el-button>
                                                </div>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-card>
                            </div>

                            <!-- APP安装包 -->
                            <div class="tool-section">
                                <div class="section-title">
                                    <i class="el-icon-mobile"></i>
                                    <span>APP安装包</span>
                                </div>
                                <el-card class="table-card" shadow="hover">
                                    <el-table :data="apkPath" style="width: 100%">
                                        <el-table-column prop="platform" label="渠道名称" width="180"></el-table-column>
                                        <el-table-column prop="test_url" label="测试">
                                            <template slot-scope="scope">
                                                <div class="link-wrapper">
                                                    <el-link type="primary" :underline="false" :href="scope.row.test_url" target="_blank">{{ scope.row.test_url }}</el-link>
                                                    <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.test_url)">复制链接</el-button>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="pre_url" label="预发">
                                            <template slot-scope="scope">
                                                <div class="link-wrapper">
                                                    <el-link type="primary" :underline="false" :href="scope.row.pre_url" target="_blank">{{ scope.row.pre_url }}</el-link>
                                                    <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.pre_url)">复制链接</el-button>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="prod_url" label="生产">
                                            <template slot-scope="scope">
                                                <div class="link-wrapper">
                                                    <el-link type="primary" :underline="false" :href="scope.row.prod_url" target="_blank">{{ scope.row.prod_url }}</el-link>
                                                    <el-button type="success" size="mini" class="copy-btn" @click="copyLink(scope.row.prod_url)">复制链接</el-button>
                                                </div>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-card>
                            </div>
                        </div>
                    </div>

                    <!-- 图片生成 -->
                    <div v-show="activeMenu === 'image'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-picture"></i> 图片生成</h2>
                        </div>
                        <div class="section-content">
                            <el-row :gutter="20">
                                <el-col :md="8" :lg="6" :xl="4">
                                    <el-card class="input-card" shadow="hover">
                                        <div class="input-group">
                                            <label>图片宽度：</label>
                                            <el-input type="number" placeholder="请输入图片宽度" v-model.number="width" clearable></el-input>
                                        </div>
                                        <div class="input-group">
                                            <label>图片高度：</label>
                                            <el-input type="number" placeholder="请输入图片高度" v-model.number="height" clearable></el-input>
                                        </div>
                                        <div class="input-group">
                                            <label>图片数量：</label>
                                            <el-input type="number" placeholder="请输入图片数量" v-model.number="quantity" clearable></el-input>
                                        </div>
                                        <div class="button-group">
                                            <el-button type="primary" :loading="btStatus" @click="createImg">生成图片</el-button>
                                            <el-button type="danger" @click="clearData">数据重置</el-button>
                                        </div>
                                    </el-card>
                                </el-col>
                                <el-col :md="16" :lg="18" :xl="20">
                                    <el-card class="preview-card" shadow="hover">
                                        <div class="preview-header">
                                            <h3>图片展示区</h3>
                                            <p class="preview-tip">温馨提示：由于图片是从远程服务端获取，可能出现加载延迟，请耐心等待哦！</p>
                                        </div>
                                        <el-divider></el-divider>
                                        <div v-if="imgList.length==0" class="img-list">
                                            <el-empty description="这里空空如也！"></el-empty>
                                        </div>
                                        <div v-else-if="imgList.length>0" class="img-list">
                                            <img :src="img" class="img-data" alt="" v-for="img in imgList">
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <!-- 约苗埋点数据解密 -->
                    <div v-show="activeMenu === 'decrypt'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-key"></i> 约苗埋点数据解密</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="decrypt-card" shadow="hover">
                                <div class="decrypt-header">
                                    <h3>工具说明</h3>
                                    <div class="decrypt-tips">
                                        <p class="tip-item"><i class="el-icon-warning"></i> ANDROID APP请使用AES加解密工具</p>
                                        <p class="tip-item"><i class="el-icon-warning"></i> IOS APP、微信公号请使用Base64加解密工具</p>
                                    </div>
                                </div>
                                <el-tabs v-model="encryptTool" @tab-click="handleClick" class="decrypt-tabs">
                                    <el-tab-pane label="Base64加解密" name="base64">
                                        <div class="button-group">
                                            <el-button type="primary" @click="encrypt">Base64加密</el-button>
                                            <el-button type="primary" @click="copyToClipboard">解密并复制</el-button>
                                            <el-button type="danger" @click="clsData">数据重置</el-button>
                                        </div>
                                        <div class="decrypt-content">
                                            <el-row :gutter="20">
                                                <el-col :span="8">
                                                    <div class="input-section">
                                                        <h4>原始加密数据</h4>
                                                        <el-input type="textarea" v-model="inputText" :autosize="{ minRows: 13.6, maxRows: 13.6 }" placeholder="请输入加密文本"></el-input>
                                                    </div>
                                                    <div class="input-section" v-show="show">
                                                        <h4>原始解密数据</h4>
                                                        <el-input type="textarea" v-model="outputText" :autosize="{ minRows: 30, maxRows: 30 }" placeholder="原数据"/>
                                                    </div>
                                                </el-col>
                                                <el-col :span="16">
                                                    <div class="output-section">
                                                        <h4>格式化解密数据</h4>
                                                        <el-input type="textarea" v-model="outputJson" :autosize="{ minRows: 47, maxRows: 47 }" placeholder="格式化数据"/>
                                                    </div>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-tab-pane>
                                    <el-tab-pane label="AES加解密" name="AES">
                                        <div class="button-group">
                                            <el-button type="primary" @click="aesEncrypt">AES加密</el-button>
                                            <el-button type="primary" @click="aesDecryptAndCopy">解密并复制</el-button>
                                            <el-button type="danger" @click="clsData">数据重置</el-button>
                                        </div>
                                        <div class="decrypt-content">
                                            <el-row :gutter="20">
                                                <el-col :span="8">
                                                    <div class="input-section">
                                                        <h4>原始加密数据</h4>
                                                        <el-input type="textarea" v-model="inputText" :autosize="{ minRows: 13.6, maxRows: 13.6 }" placeholder="请输入加密文本"></el-input>
                                                    </div>
                                                    <div class="input-section" v-show="show">
                                                        <h4>原始解密数据</h4>
                                                        <el-input type="textarea" v-model="outputText" :autosize="{ minRows: 30, maxRows: 30 }" placeholder="原数据"/>
                                                    </div>
                                                </el-col>
                                                <el-col :span="16">
                                                    <div class="output-section">
                                                        <h4>格式化解密数据</h4>
                                                        <el-input type="textarea" v-model="outputJson" :autosize="{ minRows: 47, maxRows: 47 }" placeholder="格式化数据"/>
                                                    </div>
                                                </el-col>
                                            </el-row>
                                        </div>
                                    </el-tab-pane>
                                </el-tabs>
                            </el-card>
                        </div>
                    </div>

                    <!-- Doris -->
                    <div v-show="activeMenu === 'doris'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-data-analysis"></i> Doris</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="doris-card" shadow="hover">
                                <div class="doris-header">
                                    <h3>注意事项</h3>
                                    <div class="doris-tips">
                                        <p class="tip-item"><i class="el-icon-info"></i> 相关数据表：tb_tag(标签) tb_base_tag(标签查询规则) tb_tag_users(用户标签明细) tb_user_group(用户分群) tb_user_group_user(用户分群明细) tb_user_group_task(用户分群任务)</p>
                                        <p class="tip-item"><i class="el-icon-info"></i> 新建分群在整十分会自动更新用户分群的数据、历史分群每天凌晨1-2点全量更新</p>
                                        <p class="tip-item"><i class="el-icon-info"></i> 查看日志路径：tail -f /data/task-{env}/logs/scheduler.log</p>
                                        <p class="tip-item"><i class="el-icon-info"></i> 若当天标签数据需要重新生成，请手动在tb_tag_users表中删除对应id的标签，tb_tag表中对应的标签的update_time清空，在执行用户标签脚本</p>
                                        <p class="tip-item"><i class="el-icon-info"></i> 若当天分群数据需要重新生成，清除分群的tb_user_group_task对应任务数据，然后把分群任务设置为手动更新，再执行用户分群脚本</p>
                                    </div>
                                </div>
                                <el-row :gutter="20">
                                    <el-col :span="8">
                                        <el-card class="task-card" shadow="hover">
                                            <div class="task-header">
                                                <h3>任务列表</h3>
                                            </div>
                                            <div class="task-section">
                                                <h4>测试环境-数据同步</h4>
                                                <div class="task-buttons">
                                                    <el-button type="primary" @click="TestTag">用户标签</el-button>
                                                    <el-button type="primary" @click="TestCluster">用户分群</el-button>
                                                    <el-button type="primary" @click="TestIncident">数据概览</el-button>
                                                    <el-button type="primary" @click="TestOverview">事件统计</el-button>
                                                </div>
                                            </div>
                                            <el-divider></el-divider>
                                            <div class="task-section">
                                                <h4>预发环境-数据同步</h4>
                                                <div class="task-buttons">
                                                    <el-button type="danger" @click="PreTag">用户标签</el-button>
                                                    <el-button type="danger" @click="PreCluster">用户分群</el-button>
                                                    <el-button type="danger" @click="PreIncident">数据概览</el-button>
                                                    <el-button type="danger" @click="PreOverview">事件统计</el-button>
                                                </div>
                                            </div>
                                        </el-card>
                                    </el-col>
                                    <el-col :span="16">
                                        <el-card class="command-card" shadow="hover">
                                            <div class="command-header">
                                                <el-badge value="持续更新中...">
                                                    <h3>Doris常用命令和函数</h3>
                                                </el-badge>
                                                <el-link type="warning" :underline="false" :href="dorisLink" target="_blank">Doris操作手册</el-link>
                                            </div>
                                            <el-tabs v-model="funcName" class="function-tabs">
                                                <el-tab-pane label="命令" name="zero">
                                                    <el-table :data="commandList" border style="width: 100%">
                                                        <el-table-column prop="cmd" label="命令"></el-table-column>
                                                        <el-table-column prop="remark" label="备注"></el-table-column>
                                                    </el-table>
                                                </el-tab-pane>
                                                <el-tab-pane label="时间类" name="first">
                                                    <el-table :data="timeClass" border style="width: 100%">
                                                        <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                        <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                        <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                                    </el-table>
                                                </el-tab-pane>
                                                <el-tab-pane label="聚合类" name="second">
                                                    <el-table :data="aggregationClass" border style="width: 100%">
                                                        <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                        <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                        <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                                    </el-table>
                                                </el-tab-pane>
                                                <el-tab-pane label="Bitmap类" name="third">
                                                    <el-table :data="bitmapClass" border style="width: 100%">
                                                        <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                        <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                        <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                                    </el-table>
                                                </el-tab-pane>
                                                <el-tab-pane label="其他类" name="four">
                                                    <el-table :data="otherClass" border style="width: 100%">
                                                        <el-table-column prop="functionName" label="函数名" width="150"></el-table-column>
                                                        <el-table-column prop="functionUsage" label="用法" width="500"></el-table-column>
                                                        <el-table-column prop="functionRemark" label="备注"></el-table-column>
                                                    </el-table>
                                                </el-tab-pane>
                                                <el-tab-pane label="常用SQL" name="five">
                                                    <el-table :data="sqlClass" border style="width: 100%">
                                                        <el-table-column prop="sqlName" label="SQL名称" width="150"></el-table-column>
                                                        <el-table-column prop="sqlValue" label="SQL语句" width="1000"></el-table-column>
                                                        <el-table-column prop="remark" label="备注"></el-table-column>
                                                    </el-table>
                                                </el-tab-pane>
                                            </el-tabs>
                                        </el-card>
                                    </el-col>
                                </el-row>
                            </el-card>
                        </div>
                    </div>

                    <!-- 其他工具 -->
                    <div v-show="activeMenu === 'idcard'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-document"></i> 身份证生成</h2>
                        </div>
                        <div class="section-content">
                            <div class="tools-grid">
                                <el-card class="tool-card" shadow="hover">
                                    <div class="tool-card-content">
                                        <iframe src="https://sfz.fatcarter.cn/" class="my-iframe" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                    </div>
                                </el-card>
                            </div>
                        </div>
                    </div>

                    <!-- Base64加解密 -->
                    <div v-show="activeMenu === 'base64'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-key"></i> Base64加解密</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <iframe src="https://tools.geekzhan.com/base64/" class="my-iframe" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- SM4加解密 -->
                    <div v-show="activeMenu === 'sm4'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-lock"></i> SM4加解密</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <div class="sm4-content">
                                        <el-row :gutter="20">
                                            <el-col :span="11">
                                                <div class="input-section">
                                                    <h4>待加密/解密的文本</h4>
                                                    <el-input type="textarea" v-model="sm4inputText" :autosize="{ minRows: 13.6, maxRows: 13.6 }" placeholder="请输入加密/解密文本"></el-input>
                                                </div>
                                            </el-col>
                                            <el-col :span="2">
                                                <div class="button-group-vertical">
                                                    <el-button type="primary" @click="sm4Encoding">SM4加密</el-button>
                                                    <el-button type="success" @click="sm4Decoding">SM4解密</el-button>
                                                    <el-button type="info" @click="copySm4">复制结果</el-button>
                                                </div>
                                            </el-col>
                                            <el-col :span="11">
                                                <div class="output-section">
                                                    <h4>加密/解密后数据</h4>
                                                    <el-input type="textarea" v-model="sm4inputText1" :autosize="{ minRows: 13.6, maxRows: 13.6 }" readonly></el-input>
                                                </div>
                                            </el-col>
                                        </el-row>
                                        <div class="external-link">
                                            <img src="data/icon/sm4icon.png" alt="SM4 Icon">
                                            <a href="https://www.toolhelper.cn/SymmetricEncryption/SM4" target="_blank">
                                                <i class="el-icon-link"></i>
                                                对解密结果不满意？点击跳转外部解密网站
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- Json格式化 -->
                    <div v-show="activeMenu === 'json'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-document"></i> Json格式化</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <iframe src="https://www.json.cn/json/jsononline.html"class="my-iframe" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- AES加密解析 -->
                    <div v-show="activeMenu === 'aes'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-key"></i> AES加密解析</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <div class="aes-info">
                                        <pre>AES加密密码：yuemiao_zW4NcAbR
填充：pkcs5padding
字符集：utf8编码（unicode编码）</pre>
                                    </div>
                                    <iframe src="http://tool.chacuo.net/cryptaes" class="my-iframe" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- URL编码解码 -->
                    <div v-show="activeMenu === 'url'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-link"></i> URL编码解码</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <iframe src="https://tools.geekzhan.com/urlencode/" class="my-iframe" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- 时间戳转换 -->
                    <div v-show="activeMenu === 'timestamp'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-time"></i> 时间戳转换</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <iframe src="http://zaixian.pro/chengyucidian" class="my-iframe" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- 经纬度查询 -->
                    <div v-show="activeMenu === 'location'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-location"></i> 经纬度查询</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <iframe src="http://jingweidu.757dy.com/" class="my-iframe" frameborder="0" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- Cron表达式 -->
                    <div v-show="activeMenu === 'cron'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-timer"></i> Cron表达式</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="tool-card" shadow="hover">
                                <div class="tool-card-content">
                                    <iframe src="http://cron.ciding.cc/" class="my-iframe" allowfullscreen sandbox="allow-same-origin allow-scripts allow-popups allow-forms"></iframe>
                                </div>
                            </el-card>
                        </div>
                    </div>

                    <!-- 版本更新日志 -->
                    <div v-show="activeMenu === 'logs'" class="section-container">
                        <div class="section-header">
                            <h2><i class="el-icon-notebook-2"></i> 版本更新日志</h2>
                        </div>
                        <div class="section-content">
                            <el-card class="log-card" shadow="hover">
                                <el-timeline>
                                    <el-timeline-item v-for="item in logData" :key="item.title" :timestamp="item.time" icon="el-icon-more" type="primary" placement="top">
                                        <el-card shadow="hover">
                                            <h4>{{item.title}}</h4>
                                            <p>{{item.commit_time}}</p>
                                            <p>{{item.commit_content}}</p>
                                        </el-card>
                                    </el-timeline-item>
                                </el-timeline>
                            </el-card>
                        </div>
                    </div>

                    <el-backtop target=".operation-wrapper"><i class="el-icon-caret-top"></i></el-backtop>
                </div>
            </el-main>
        </el-container>
    </div>
    <script>
        let app = new Vue({
            el: "#app",
            data: {
                "tabPosition" :"left",
                "width" :"",
                "height":"",
                "quantity":"",
                "btShow":false,
                "imgList":[],
                "btStatus":false,
                "activeName": '1',
                "activeMenu": 'workbench',
                "jsonStr":"",
                "logo":"https://img.scmttec.com/gw/laiyuemiao/<EMAIL>",
                "dorisLink":"https://doris.incubator.apache.org/zh-CN/docs/gettingStarted/what-is-new",
                funcName: "zero",
                "yamlContent": '',
                "errorOutput": '',
                "inputText": '',
                "sm4inputText1": '',
                "sm4inputText": '',
                "outputText": '',
                inputJson: '',
                outputJson: '',
                "tableData": [],
                "apkPath": [],
                logData: [],
                aiData: [],
                securityData: [],
                testPlatfromData: [],
                commandList: [],
                timeClass: [],
                aggregationClass: [],
                bitmapClass: [],
                sqlClass: [],
                otherClass: [],
                show: true,
                copyButtonIndex: -1,
                copyButtonType: '',
                visible:false,
                testUrl: 'http://192.168.20.246:3301',
                preUrl: 'http://192.168.20.246:3302',
                encryptTool:"base64",
                aesKey: "yuemiao_zW4NcAbR",
                menuLoadingStates: {} // 记录菜单加载状态
            },
            // 优化数据加载 - 懒加载策略
            async created() {
                // 延迟加载非关键数据，优先保证菜单快速渲染
                this.$nextTick(() => {
                    // 使用 setTimeout 将数据加载推迟到下一个事件循环
                    setTimeout(async () => {
                        try {
                            // 分批加载数据，避免阻塞UI
                            await this.loadCoreData();
                        } catch (error) {
                            console.error('数据加载失败:', error);
                            this.showMessage('部分数据加载失败，功能可能受限', 'warning');
                        }
                    }, 100); // 延迟100ms，让菜单先渲染
                });
            },
            mounted() {

            },
            methods:{
                // 分批加载核心数据
                async loadCoreData() {
                    const loadTasks = [
                        () => this.getPlatformUrl(),
                        () => this.getApkUrl(),
                        () => this.getAiUrl(),
                        () => this.getSecurityUrl(),
                        () => this.getTestPlatfromUrl()
                    ];

                    // 分批执行，每批间隔50ms，避免阻塞UI
                    for (let i = 0; i < loadTasks.length; i++) {
                        try {
                            await loadTasks[i]();
                            // 每加载一个数据源后稍作延迟
                            if (i < loadTasks.length - 1) {
                                await new Promise(resolve => setTimeout(resolve, 50));
                            }
                        } catch (error) {
                            console.warn(`数据源 ${i + 1} 加载失败:`, error);
                        }
                    }
                },
                createImg(){
                    if(this.width=="" || this.height=="" || this.quantity==""){
                        this.showMessage('图片宽度、高度和数量不能为空！', 'warning');
                    }else{
                        this.imgList = [];
                        this.btStatus = true
                        for( let i = 0 ; i<this.quantity ; i++ ){
                            setTimeout(() => {
                                axios
                                    .get("https://picsum.photos/"+this.width+"/"+this.height)
                                    .then(response =>{
                                        this.btShow=true;
                                        this.imgList.push(response.request.responseURL)
                                        this.showMessage('已生成：'+this.imgList.length+"/"+this.quantity, 'success');
                                    })
                            }, 500*i);
                        }
                        setTimeout(() =>{
                            this.btStatus = false
                        }, 3000)
                    }
                },
                getPlatformUrl(){
                    return axios.get("./data/platformUrl.json")
                    .then(response => {
                        this.tableData = response.data.data.platformUrl
                    })
                    .catch(error => {
                        console.warn('平台URL数据加载失败:', error)
                        throw error;
                    })
                },
                getApkUrl(){
                    return axios.get("./data/apkUrl.json")
                    .then(response => {
                        this.apkPath = response.data.data.apkUrl
                    })
                    .catch(error => {
                        console.warn('APK URL数据加载失败:', error)
                        throw error;
                    })
                },
                getLogs(){
                    return axios.get("./data/log.json")
                    .then(response => {
                        this.logData = response.data.data.logs
                    })
                    .catch(error => {
                        console.warn('日志数据加载失败:', error)
                        throw error;
                    })
                },
                getAiUrl(){
                    return axios.get("./data/ai.json")
                    .then(response => {
                        this.aiData = response.data.data.aiUrl
                    })
                    .catch(error => {
                        console.warn('AI URL数据加载失败:', error)
                        throw error;
                    })
                },
                getSecurityUrl(){
                    return axios.get("./data/security.json")
                    .then(response => {
                        this.securityData = response.data.data.securityUrl
                    })
                    .catch(error => {
                        console.warn('安全测试数据加载失败:', error)
                        throw error;
                    })
                },
                getTestPlatfromUrl(){
                    return axios.get("./data/testPlatform.json")
                    .then(response => {
                        this.testPlatfromData = response.data.data.testPlatfromUrl
                    })
                    .catch(error => {
                        console.warn('测试平台数据加载失败:', error)
                        throw error;
                    })
                },
                getFunctionList(){
                    return axios.get("./data/function.json")
                    .then(response => {
                        const dataAll = response.data.data
                        this.commandList = dataAll.commandList
                        this.timeClass = dataAll.timeClass
                        this.aggregationClass = dataAll.aggregationClass
                        this.bitmapClass = dataAll.bitmapClass
                        this.sqlClass = dataAll.sqlClass
                        this.otherClass = dataAll.otherClass
                    })
                    .catch(error => {
                        console.warn('Doris函数数据加载失败:', error)
                        throw error;
                    })
                },
                clearData(){
                    this.width=""
                    this.height=""
                    this.imgList=[]
                    this.quantity=""
                    this.btShow=false
                    this.showMessage('数据重置成功！')
                    // location.reload()
                },
                clickFormat(){
                    if(this.jsonStr == ""){
                        this.showMessage('Json字符串不能为空', 'warning');
                    }
                    // 1、JSON.parse：把JSON字符串转换为JSON对象
                    // 2、JSON.stringify：把JSON对象 转换为 有缩进的 JSON字符串格式
                    this.jsonStr = JSON.stringify(JSON.parse(this.jsonStr), null, '\t')
                },
                clearFormat(){
                    this.jsonStr = "";
                    this.showMessage('数据清空成功！')
                },
                onZip(){
                    if (this.jsonStr.substr(0, 1) === '<' && this.jsonStr.substr(-1, 1) === '>') {
                        try{
                            this.jsonStr = vkbeautify.xmlmin(this.jsonStr,true);
                        }catch(e){
                            this.$refs.alert.show('error','JSON解析失败:'+e.message,1000);
                        }
                    }else{
                        try{
                            this.jsonStr = JSON.stringify(JSON.parse(this.jsonStr));
                        }catch(e){
                            this.$refs.alert.show('error','JSON解析失败:'+e.message,1000);
                        }
                    }
                },
                strToHex(key) {
                    let hexKey= '';
                    for (let i = 0; i < key.length; i++) {
                        let code = key.charCodeAt(i);
                        if (code < 16) hex += '0';
                        hexKey += code.toString(16).toUpperCase();
                    }
                    return hexKey;
                },
                hexStringToString(hexStr) {
                    // 确保16进制字符串不包含空格或换行符
                    hexStr = hexStr.replace(/\s+/g, '');
                    // 检查16进制字符串长度是否为偶数
                    if (hexStr.length % 2 !== 0) {
                        throw new Error('Invalid hex string');
                    }
                    // 将16进制字符串转换为普通字符串
                    let str = '';
                    for (let i = 0; i < hexStr.length; i += 2) {
                        // 读取两个16进制字符并转换为对应的字符
                        const byte = parseInt(hexStr.substr(i, 2), 16);
                        if (!isNaN(byte)) {
                            str += String.fromCharCode(byte);
                        }
                    }
                    return str;
                },
                sm4Encoding() {
                    const key = "1234567812345678";
                    const hexk = this.strToHex(key);
                    if (this.sm4inputText == "") {
                        this.showMessage('请输入加密文本！', 'warning');
                    } else {
                        const re = sm4.encrypt(this.sm4inputText, hexk);
                        this.sm4inputText1 = window.btoa(this.hexStringToString(re));
                        this.showMessage('加密成功', 'success');
                    }
                },
                base64ToHex(base64) {
                  const binaryData = atob(base64);
                  const len = binaryData.length;
                  let bytes = new Uint8Array(len);
                  for (let i = 0; i < len; i++) {
                    bytes[i] = binaryData.charCodeAt(i);
                  }
                  let hexString = Array.from(bytes).map(function(byte) {
                    return ('0' + (byte & 0xFF).toString(16)).slice(-2).toUpperCase();
                  }).join('');
                  return hexString;
                },
                sm4Decoding() {
                    const key = "1234567812345678";
                    const hexk = this.strToHex(key);
                    var globalre = "";
                    if (this.sm4inputText == "") {
                        this.showMessage('请输入解密文本！', 'warning');
                        this.sm4inputText1 = '';
                    } else {
                        try {
                            let hexinput = this.base64ToHex(this.sm4inputText);
                            let ree = sm4.decrypt(hexinput, hexk);
                            globalre = ree;
                        } catch (e) {
                            this.showMessage('解密异常，格式不正确', 'error');
                            this.sm4inputText1 = '';
                            return;
                        }
                        if (globalre == "") {
                            this.showMessage('解密结果为空，请输入合法数据', 'error');
                            this.sm4inputText1 = '';
                            return;
                        }
                        this.sm4inputText1 = globalre;
                        this.showMessage('解密成功', 'success');
                    }
                },
                copySm4() {
                    if (this.sm4inputText1 == "") {
                        this.showMessage('不能复制空文本', 'warning');
                    } else {
                        const textarea = document.createElement('textarea');
                        textarea.value = this.sm4inputText1;
                        document.body.appendChild(textarea);
                        textarea.select();
                        try {
                            var successful = document.execCommand('copy');
                            this.showMessage(successful ? '复制成功' : '复制失败', 'success');
                        } catch (err) {
                            this.showMessage('无法复制', 'error');
                        }
                        document.body.removeChild(textarea);
                    }
                },
                async encrypt() {
                    if(this.inputText==""){
                        this.showMessage('加密文本不能为空！！', 'warning');
                        return;
                    }

                    // 确保 Base64 库已加载
                    await this.ensureBase64Loaded();

                    if (typeof Base64 === 'undefined') {
                        this.showMessage('Base64 库加载失败，请刷新页面重试', 'error');
                        return;
                    }

                    try {
                        this.outputText = Base64.encode(this.inputText);
                        this.showMessage('加密成功', 'success');
                    } catch (error) {
                        console.error('Base64 加密失败:', error);
                        this.showMessage('加密失败，请检查输入内容', 'error');
                    }
                },
                async copyToClipboard() {
                    if(this.inputText==""){
                        this.showMessage('加密文本不能为空！！', 'warning');
                        return;
                    }

                    // 确保 Base64 库已加载
                    await this.ensureBase64Loaded();

                    if (typeof Base64 === 'undefined') {
                        this.showMessage('Base64 库加载失败，请刷新页面重试', 'error');
                        return;
                    }

                    try {
                        this.outputText = Base64.decode(this.inputText);
                        this.inputJson = JSON.parse(this.outputText);
                        this.outputJson = JSON.stringify(this.inputJson, null, 2);

                        const textarea = document.createElement('textarea')
                        textarea.value = this.outputJson
                        document.body.appendChild(textarea)
                        textarea.select()
                        document.execCommand('copy')
                        document.body.removeChild(textarea)
                        this.showMessage('解密并复制成功', 'success');
                    } catch (error) {
                        console.error('Base64 解密失败:', error);
                        this.showMessage('解密失败，请检查输入内容格式', 'error');
                    }
                },
                clsData() {
                    this.inputText = "";
                    this.inputJson = "";
                    this.outputText = "";
                    this.outputJson = "";
                    this.$message('数据重置成功！');
                },
                async requestWithMessage(baseUrl, urlPath, message){
                    try{
                        const response = await axios.get(baseUrl + urlPath);
                        this.showMessage(message, 'success');
                    }catch(error){
                        console.log(error);
                    }
                },
                showMessage(message, type = 'info') {
                    this.$message({
                        showClose: true,
                        message: message,
                        type: type
                    });
                },
                TestCluster(){
                    this.confirmAndExecuteOperation(this.testUrl, "/trigger", "Test用户分群同步成功！")              
                },
                TestTag(){
                    this.confirmAndExecuteOperation(this.testUrl, "/tag", "Test用户标签同步成功！")
                },
                TestIncident(){
                    this.confirmAndExecuteOperation(this.testUrl, "/user-trend", "Test数据概览同步成功！")
                },
                TestOverview(){
                    this.confirmAndExecuteOperation(this.testUrl, "/event-statistic", "Test数据概览同步成功！")
                },
                PreCluster(){
                    this.confirmAndExecuteOperation(this.preUrl, "/trigger", "Pre用户分群同步成功！")
                },
                PreTag(){
                    this.confirmAndExecuteOperation(this.preUrl, "/tag", "Pre用户标签同步成功！")
                },
                PreIncident(){
                    this.confirmAndExecuteOperation(this.preUrl, "/user-trend", "Pre数据概览同步成功！")
                },
                PreOverview(){
                    this.confirmAndExecuteOperation(this.preUrl, "/event-statistic", "Pre事件统计同步成功！")
                },
                confirmAndExecuteOperation(baseUrl, urlPath, message) {
                    this.$confirm('是否执行该操作?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    this.requestWithMessage(baseUrl, urlPath, message)
                    this.showMessage('执行成功', 'success');
                }).catch(() => {
                    this.showMessage('取消执行');        
                    });
                },
                showCopyButton(index, type) {
                    this.copyButtonIndex = index;
                    this.copyButtonType = type;
                },
                hideCopyButton() {
                    this.copyButtonIndex = -1;
                    this.copyButtonType = '';
                },
                copyLink(url) {
                    const el = document.createElement('textarea');
                    el.value = url;
                    document.body.appendChild(el);
                    el.select();
                    document.execCommand('copy');
                    document.body.removeChild(el);
                    this.$message.success('链接已复制到剪贴板');
                },
                aesEncrypt(){
                  if(this.inputText==""){
                        this.showMessage('加密文本不能为空！！', 'warning');
                    }else{
                        this.outputText =  CryptoJS.AES.encrypt(this.inputText, this.aesKey).toString();
                        this.showMessage('加密成功', 'success');
                    }
                },
                async aesDecryptAndCopy(){
                  if(this.inputText==""){
                        this.showMessage('加密文本不能为空！！', 'warning');
                    }else{
                      const key = CryptoJS.enc.Utf8.parse(this.aesKey);
                      try {
                        console.log(this.inputText);
                        const bytes = CryptoJS.AES.decrypt(this.inputText, key, {padding: CryptoJS.pad.Pkcs7,mode: CryptoJS.mode.ECB});
                        this.outputText = bytes.toString(CryptoJS.enc.Utf8);
                        try {
                          this.outputJson = JSON.stringify(JSON.parse(this.outputText), null, 2);
                        } catch (e) {
                          console.error('无法解析为 JSON 格式:', e);
                          this.outputJson = this.outputText;
                        }
                        const textarea = document.createElement('textarea')
                        textarea.value = this.outputJson
                        document.body.appendChild(textarea)
                        textarea.select()
                        document.execCommand('copy')
                        document.body.removeChild(textarea)
                        this.showMessage('解密并复制成功', 'success');
                        } catch(e){
                          console.error('解密失败:', e);
                          this.showMessage('解密失败，请检查输入文本和密钥', 'warning');
                        }
                    }
                },
                handleClick(tab) {
                  // 清空所有输入文本
                  this.outputText = '';
                  this.outputJson = '';
                  this.show = true;
                },
                // 防抖处理菜单选择
                handleMenuSelect: (function() {
                    let debounceTimer = null;
                    return function(key) {
                        // 立即更新菜单状态，避免延迟感
                        this.activeMenu = key;

                        // 清除之前的定时器
                        if (debounceTimer) {
                            clearTimeout(debounceTimer);
                        }

                        // 防抖加载数据
                        debounceTimer = setTimeout(async () => {
                            await this.loadMenuData(key);
                        }, 150); // 150ms防抖延迟
                    };
                })(),

                // 按需加载菜单相关数据
                async loadMenuData(key) {
                    try {
                        // 使用 Promise.all 并行加载相关资源
                        const loadPromises = [];

                        // Doris 页面数据加载
                        if (key === 'doris' && this.commandList.length === 0) {
                            loadPromises.push(this.getFunctionList());
                        }

                        // 日志页面数据加载
                        if (key === 'logs' && this.logData.length === 0) {
                            loadPromises.push(this.getLogs());
                        }

                        // 加密工具库加载
                        if (['decrypt', 'base64', 'sm4', 'aes'].includes(key)) {
                            loadPromises.push(this.loadCryptoLibsAsync());
                        }

                        // 并行执行所有加载任务
                        if (loadPromises.length > 0) {
                            await Promise.all(loadPromises);
                        }
                    } catch (error) {
                        console.warn(`菜单 ${key} 相关数据加载失败:`, error);
                        // 不阻断用户操作，只显示警告
                        this.showMessage('部分功能数据加载失败，可能影响使用', 'warning');
                    }
                },

                // 异步加载加密库
                loadCryptoLibsAsync() {
                    return new Promise((resolve) => {
                        if (window.cryptoLibsLoaded) {
                            resolve();
                            return;
                        }

                        // 使用 requestIdleCallback 在浏览器空闲时加载
                        const loadLibs = () => {
                            window.loadCryptoLibs();
                            resolve();
                        };

                        if (window.requestIdleCallback) {
                            window.requestIdleCallback(loadLibs);
                        } else {
                            setTimeout(loadLibs, 0);
                        }
                    });
                },

                // 确保 Base64 库已加载
                ensureBase64Loaded() {
                    return new Promise((resolve) => {
                        // 如果 Base64 已经可用，直接返回
                        if (typeof Base64 !== 'undefined') {
                            resolve();
                            return;
                        }

                        // 等待一小段时间让库加载完成
                        let attempts = 0;
                        const maxAttempts = 20; // 最多等待1秒
                        const checkInterval = setInterval(() => {
                            attempts++;
                            if (typeof Base64 !== 'undefined') {
                                clearInterval(checkInterval);
                                resolve();
                            } else if (attempts >= maxAttempts) {
                                clearInterval(checkInterval);
                                console.error('Base64 库加载超时');
                                resolve(); // 即使失败也要 resolve，让调用方处理错误
                            }
                        }, 50);
                    });
                }
            }
        })
    </script>

    <!-- 按需加载的工具库 -->
    <script>
        // 延迟加载非关键JS库
        function loadScript(src, callback) {
            const script = document.createElement('script');
            script.src = src;
            script.onload = callback || function() {};
            script.onerror = function() {
                console.error('脚本加载失败:', src);
                if (callback) callback();
            };
            document.head.appendChild(script);
        }

        function loadCSS(href) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
        }

        // 当需要使用特定功能时再加载对应的库
        window.loadCryptoLibs = function(callback) {
            if (window.cryptoLibsLoaded) {
                if (callback) callback();
                return;
            }

            let loadedCount = 0;
            const totalLibs = 3;

            function onLibLoaded() {
                loadedCount++;
                if (loadedCount === totalLibs) {
                    window.cryptoLibsLoaded = true;
                    if (callback) callback();
                }
            }

            loadScript('./lib/js/js-base64-main/base64.js', onLibLoaded);
            loadScript('https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js', onLibLoaded);
            loadScript('./lib/js/sm-crypto/dist/sm4.js', onLibLoaded);
        };

        window.loadCodeMirror = function() {
            if (!window.codeMirrorLoaded) {
                loadCSS('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.css');
                loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/codemirror.min.js', function() {
                    loadScript('https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.62.0/mode/yaml/yaml.min.js');
                });
                window.codeMirrorLoaded = true;
            }
        };
    </script>
</body>
</html>