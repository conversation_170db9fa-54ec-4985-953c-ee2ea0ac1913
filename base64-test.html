<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64 功能测试</title>
    <link rel="stylesheet" href="https://img.scmttec.com/hospital/libs/element-ui2.15.6/theme-chalk/index.min.css">
    <script src="./lib/js/vue.min.js"></script>
    <script src="https://img.scmttec.com/hospital/libs/element-ui.min.js"></script>
    <script src="./lib/js/js-base64-main/base64.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-title {
            color: #333;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            border-bottom: 2px solid #409EFF;
            padding-bottom: 10px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success {
            background-color: #67C23A;
        }
        .status-error {
            background-color: #F56C6C;
        }
        .status-warning {
            background-color: #E6A23C;
        }
        .test-result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .result-success {
            background-color: #f0f9ff;
            border: 1px solid #67C23A;
            color: #67C23A;
        }
        .result-error {
            background-color: #fef0f0;
            border: 1px solid #F56C6C;
            color: #F56C6C;
        }
        .button-group {
            margin: 20px 0;
        }
        .el-button {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>🔧 Base64 功能测试工具</h1>
            
            <div class="test-section">
                <div class="test-title">
                    <span class="status-indicator" :class="base64Status"></span>
                    Base64 库状态检测
                </div>
                <el-button type="primary" @click="checkBase64Status">检测 Base64 库</el-button>
                <div v-if="base64Result" class="test-result" :class="base64ResultClass">{{ base64Result }}</div>
            </div>

            <div class="test-section">
                <div class="test-title">
                    <span class="status-indicator" :class="encodeStatus"></span>
                    Base64 编码测试
                </div>
                <el-input 
                    type="textarea" 
                    v-model="testInput" 
                    placeholder="请输入要编码的文本"
                    :autosize="{ minRows: 3, maxRows: 6 }">
                </el-input>
                <div class="button-group">
                    <el-button type="success" @click="testEncode">Base64 编码</el-button>
                    <el-button type="info" @click="clearInput">清空输入</el-button>
                </div>
                <div v-if="encodeResult" class="test-result" :class="encodeResultClass">{{ encodeResult }}</div>
            </div>

            <div class="test-section">
                <div class="test-title">
                    <span class="status-indicator" :class="decodeStatus"></span>
                    Base64 解码测试
                </div>
                <el-input 
                    type="textarea" 
                    v-model="testEncoded" 
                    placeholder="请输入要解码的 Base64 文本"
                    :autosize="{ minRows: 3, maxRows: 6 }">
                </el-input>
                <div class="button-group">
                    <el-button type="warning" @click="testDecode">Base64 解码</el-button>
                    <el-button type="info" @click="clearEncoded">清空输入</el-button>
                </div>
                <div v-if="decodeResult" class="test-result" :class="decodeResultClass">{{ decodeResult }}</div>
            </div>

            <div class="test-section">
                <div class="test-title">
                    <span class="status-indicator" :class="jsonStatus"></span>
                    JSON + Base64 综合测试
                </div>
                <el-input 
                    type="textarea" 
                    v-model="testJson" 
                    placeholder="请输入 JSON 数据"
                    :autosize="{ minRows: 4, maxRows: 8 }">
                </el-input>
                <div class="button-group">
                    <el-button type="primary" @click="testJsonEncode">JSON → Base64</el-button>
                    <el-button type="success" @click="testJsonDecode">Base64 → JSON</el-button>
                    <el-button type="info" @click="clearJson">清空输入</el-button>
                </div>
                <div v-if="jsonResult" class="test-result" :class="jsonResultClass">{{ jsonResult }}</div>
            </div>

            <div class="test-section">
                <div class="test-title">预设测试数据</div>
                <div class="button-group">
                    <el-button size="small" @click="loadSampleText">加载示例文本</el-button>
                    <el-button size="small" @click="loadSampleJson">加载示例 JSON</el-button>
                    <el-button size="small" @click="loadSampleBase64">加载示例 Base64</el-button>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                testInput: '',
                testEncoded: '',
                testJson: '',
                base64Result: '',
                encodeResult: '',
                decodeResult: '',
                jsonResult: '',
                base64Status: 'status-warning',
                encodeStatus: 'status-warning',
                decodeStatus: 'status-warning',
                jsonStatus: 'status-warning'
            },
            computed: {
                base64ResultClass() {
                    return this.base64Status === 'status-success' ? 'result-success' : 'result-error';
                },
                encodeResultClass() {
                    return this.encodeStatus === 'status-success' ? 'result-success' : 'result-error';
                },
                decodeResultClass() {
                    return this.decodeStatus === 'status-success' ? 'result-success' : 'result-error';
                },
                jsonResultClass() {
                    return this.jsonStatus === 'status-success' ? 'result-success' : 'result-error';
                }
            },
            mounted() {
                this.checkBase64Status();
            },
            methods: {
                checkBase64Status() {
                    try {
                        if (typeof Base64 === 'undefined') {
                            this.base64Status = 'status-error';
                            this.base64Result = '❌ Base64 库未定义\n请检查库文件是否正确加载';
                        } else if (typeof Base64.encode === 'function' && typeof Base64.decode === 'function') {
                            this.base64Status = 'status-success';
                            this.base64Result = '✅ Base64 库加载成功\n版本: ' + (Base64.version || '未知') + '\n可用方法: encode, decode';
                        } else {
                            this.base64Status = 'status-warning';
                            this.base64Result = '⚠️ Base64 库部分功能缺失\n请检查库版本是否正确';
                        }
                    } catch (error) {
                        this.base64Status = 'status-error';
                        this.base64Result = '❌ Base64 库检测失败\n错误: ' + error.message;
                    }
                },

                testEncode() {
                    try {
                        if (!this.testInput.trim()) {
                            this.encodeStatus = 'status-warning';
                            this.encodeResult = '⚠️ 请输入要编码的文本';
                            return;
                        }

                        const encoded = Base64.encode(this.testInput);
                        this.encodeStatus = 'status-success';
                        this.encodeResult = '✅ 编码成功\n原文: ' + this.testInput + '\n编码结果: ' + encoded;
                        this.testEncoded = encoded; // 自动填充到解码输入框
                    } catch (error) {
                        this.encodeStatus = 'status-error';
                        this.encodeResult = '❌ 编码失败\n错误: ' + error.message;
                    }
                },

                testDecode() {
                    try {
                        if (!this.testEncoded.trim()) {
                            this.decodeStatus = 'status-warning';
                            this.decodeResult = '⚠️ 请输入要解码的 Base64 文本';
                            return;
                        }

                        const decoded = Base64.decode(this.testEncoded);
                        this.decodeStatus = 'status-success';
                        this.decodeResult = '✅ 解码成功\nBase64: ' + this.testEncoded + '\n解码结果: ' + decoded;
                    } catch (error) {
                        this.decodeStatus = 'status-error';
                        this.decodeResult = '❌ 解码失败\n错误: ' + error.message + '\n请检查输入的 Base64 格式是否正确';
                    }
                },

                testJsonEncode() {
                    try {
                        if (!this.testJson.trim()) {
                            this.jsonStatus = 'status-warning';
                            this.jsonResult = '⚠️ 请输入 JSON 数据';
                            return;
                        }

                        // 验证 JSON 格式
                        JSON.parse(this.testJson);
                        
                        const encoded = Base64.encode(this.testJson);
                        this.jsonStatus = 'status-success';
                        this.jsonResult = '✅ JSON → Base64 成功\n原始 JSON: ' + this.testJson + '\nBase64 结果: ' + encoded;
                        this.testEncoded = encoded;
                    } catch (error) {
                        this.jsonStatus = 'status-error';
                        this.jsonResult = '❌ JSON → Base64 失败\n错误: ' + error.message;
                    }
                },

                testJsonDecode() {
                    try {
                        if (!this.testEncoded.trim()) {
                            this.jsonStatus = 'status-warning';
                            this.jsonResult = '⚠️ 请输入 Base64 编码的 JSON 数据';
                            return;
                        }

                        const decoded = Base64.decode(this.testEncoded);
                        const jsonObj = JSON.parse(decoded);
                        const formatted = JSON.stringify(jsonObj, null, 2);
                        
                        this.jsonStatus = 'status-success';
                        this.jsonResult = '✅ Base64 → JSON 成功\nBase64: ' + this.testEncoded + '\n格式化 JSON:\n' + formatted;
                        this.testJson = formatted;
                    } catch (error) {
                        this.jsonStatus = 'status-error';
                        this.jsonResult = '❌ Base64 → JSON 失败\n错误: ' + error.message;
                    }
                },

                loadSampleText() {
                    this.testInput = 'Hello, 这是一个测试文本！包含中文和英文。';
                },

                loadSampleJson() {
                    this.testJson = JSON.stringify({
                        "name": "测试用户",
                        "age": 25,
                        "city": "北京",
                        "hobbies": ["编程", "阅读", "旅行"],
                        "timestamp": new Date().toISOString()
                    }, null, 2);
                },

                loadSampleBase64() {
                    this.testEncoded = 'eyJuYW1lIjoi5rWL6K+V55So5oi3IiwiYWdlIjoyNSwiY2l0eSI6IuWMl+S6rCJ9';
                },

                clearInput() {
                    this.testInput = '';
                    this.encodeResult = '';
                    this.encodeStatus = 'status-warning';
                },

                clearEncoded() {
                    this.testEncoded = '';
                    this.decodeResult = '';
                    this.decodeStatus = 'status-warning';
                },

                clearJson() {
                    this.testJson = '';
                    this.jsonResult = '';
                    this.jsonStatus = 'status-warning';
                }
            }
        });
    </script>
</body>
</html>
