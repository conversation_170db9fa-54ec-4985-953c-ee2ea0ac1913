:root {
    --primary-color: #409EFF;
    --primary-color-dark: #337ecc;
    --primary-color-light: #ecf5ff;
    --background-color: #f5f7fa;
    --card-background: #ffffff;
    --text-color: #303133;
    --text-color-secondary: #909399;
    --text-color-on-dark: #ffffff;
    --border-color: #e4e7ed;
    --border-radius: 8px;
    --transition-speed: 0.3s;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.02), 0 1px 2px rgba(0,0,0,0.04);
    --shadow-md: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    /* 优化字体渲染 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 优化滚动性能 */
    -webkit-overflow-scrolling: touch;
}

/* 菜单动画关键帧 */
@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 菜单项加载状态 */
.menu-loading {
    position: relative;
}

.menu-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 8px;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translateY(-50%);
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* 防止Vue加载时的闪烁 */
[v-cloak] {
    display: none;
}

.operation-wrapper {
    width: 100% !important;
    min-height: 100vh;
}

.el-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-bottom: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    line-height: 70px;
    height: 70px !important;
    padding: 0;
    position: relative;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 30px;
    width: 100%;
    box-sizing: border-box;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
}

.header-left h2 {
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    letter-spacing: 0.3px;
    white-space: nowrap;
}

.header-img {
    width: 45px;
    height: 30px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    border-radius: 4px;
}

.header-menu {
    border: none;
    background: transparent !important;
    display: flex;
    justify-content: flex-end;
    margin: 0;
    /* 优化菜单渲染性能 */
    will-change: transform;
    transform: translateZ(0);
}

.header-right {
    display: none;
}

.header-menu .el-menu-item {
    height: 70px;
    line-height: 70px;
    font-size: 14px;
    padding: 0 24px;
    transition: all 0.2s ease-out; /* 减少过渡时间，提升响应速度 */
    color: rgba(255, 255, 255, 0.9) !important;
    border-bottom: 3px solid transparent;
    margin: 0 4px;
    font-weight: 500;
    position: relative;
    /* 优化渲染性能 */
    will-change: transform, background-color;
    transform: translateZ(0);
}

.header-menu .el-menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    transform: translateY(-1px);
}

.header-menu .el-menu-item.is-active {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-weight: 600;
    border-bottom: 3px solid #40a9ff;
}

.header-menu .el-menu-item i {
    margin-right: 4px;
    font-size: 16px;
}

/* 下拉菜单样式 */
.header-menu .el-submenu {
    height: 70px;
    line-height: 70px;
}

.header-menu .el-submenu__title {
    height: 70px;
    line-height: 70px;
    font-size: 14px;
    padding: 0 24px;
    transition: all 0.2s ease-out; /* 减少过渡时间 */
    color: rgba(255, 255, 255, 0.9) !important;
    border-bottom: 3px solid transparent;
    margin: 0 4px;
    font-weight: 500;
    position: relative;
    /* 优化渲染性能 */
    will-change: transform, background-color;
    transform: translateZ(0);
}

.header-menu .el-submenu__title:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    transform: translateY(-1px);
}

.header-menu .el-submenu.is-active .el-submenu__title {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    font-weight: 600;
    border-bottom: 3px solid #40a9ff;
}

.header-menu .el-submenu__title i {
    margin-right: 4px;
    font-size: 16px;
}

/* 下拉菜单弹出层样式 */
.el-menu--popup {
    background-color: white !important;
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
    padding: 12px 0 !important;
    min-width: 200px !important;
    margin-top: 8px !important;
    /* 优化下拉菜单性能 */
    will-change: transform, opacity;
    transform: translateZ(0);
    /* 添加淡入动画 */
    animation: fadeInDown 0.2s ease-out;
}

.el-menu--popup::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.el-menu--popup .el-menu-item {
    height: 44px !important;
    line-height: 44px !important;
    padding: 0 24px !important;
    margin: 2px 12px !important;
    border-radius: 8px !important;
    color: var(--text-color) !important;
    transition: all var(--transition-speed) !important;
    border-bottom: none !important;
    font-weight: 500;
}

.el-menu--popup .el-menu-item:hover {
    background: linear-gradient(135deg, #91d5ff 0%, #bae7ff 100%) !important;
    color: #1890ff !important;
    transform: translateX(4px);
}

.el-menu--popup .el-menu-item.is-active {
    background: linear-gradient(135deg, #91d5ff 0%, #bae7ff 100%) !important;
    color: #1890ff !important;
    font-weight: 700;
}

.el-menu--popup .el-menu-item i {
    margin-right: 10px;
    font-size: 16px;
    width: 16px;
    text-align: center;
}

.el-main {
    padding: 20px !important;
    height: calc(100vh - 61px);
    overflow-y: auto;
    background-color: var(--background-color);
}

.el-tabs__item {
    transition: all var(--transition-speed);
    border-radius: var(--border-radius);
    margin: 5px;
}

.el-tabs__item.is-active {
    background-color: var(--primary-color);
    color: var(--text-color-on-dark) !important;
    font-weight: bold;
}

.el-card, .section-container, .input-card, .preview-card, .decrypt-card, .doris-card, .task-card, .command-card, .log-card {
    border-radius: var(--border-radius);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    background: var(--card-background);
    box-shadow: none;
}

.el-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.el-button, .button-group .el-button, .button-group-vertical .el-button {
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
    background-color: var(--primary-color);
    color: #fff;
    font-weight: bold;
    border: none;
}

.el-button:hover, .button-group .el-button:hover, .button-group-vertical .el-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    background-color: var(--primary-color-dark);
    color: #fff;
}

.el-input {
    width: 100%;
    max-width: 300px;
}


.el-input__inner {
    border-radius: var(--border-radius);
}

.input-style, .bt-style, .img-data, .worktable {
    margin: 20px;
}

.content {
    margin: 0 0 20px 20px;
}

.img-data {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    margin: 10px;
    border-radius: var(--border-radius);
    transition: transform var(--transition-speed);
}

.img-data:hover {
    transform: scale(1.05);
}

.img-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 20px;
}

.card-style {
    width: 200px;
    margin-right: 20px;
    background: var(--card-background);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.firing-range {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 20px;
}

.CodeMirror {
    min-height: 666px;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.doris-box {
    margin: 30px;
    background: var(--card-background);
}

.doris-content {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 10px;
}

.doris-bt {
    margin: 10px;
}

.aes-sy {
    color: var(--danger-color);
    font-weight: bold;
    font-size: 20px;
    padding: 20px;
    background: rgba(245,108,108,0.1);
    border-radius: var(--border-radius);
}

.ai-box {
    width: 100%;
    height: 100%;
    min-height: 1200px;
}

.el-timeline-item__node {
    background-color: var(--primary-color);
}

.el-timeline-item__tail {
    border-left: 2px solid var(--primary-color);
}

.el-table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: none;
    background: var(--card-background);
    transition: all var(--transition-speed);
    border: 1px solid var(--border-color);
}

.el-table:hover {
    box-shadow: var(--shadow-md);
}

.el-table th {
    background: var(--primary-color-light) !important;
    color: var(--primary-color) !important;
    font-weight: 600;
    padding: 12px 0;
}

.el-table td {
    padding: 12px 0;
    transition: all var(--transition-speed);
}

.el-table tr:hover td {
    background-color: var(--primary-color-light) !important;
}

.el-backtop {
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.el-backtop:hover {
    background-color: var(--primary-color-dark);
}

@media (max-height: 1200px) {
    .ai-box {
        min-height: 100vh;
    }
}

/* 添加动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.el-card, .el-tab-pane {
    animation: fadeIn 0.5s ease-out;
}

/* 添加滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8abb2;
}

.link-wrapper {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 4px 8px;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
    background: transparent;
}

.link-wrapper:hover {
    background: var(--primary-color-light);
}

.link-wrapper .el-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-speed);
    position: relative;
    padding: 4px 0;
}

.link-wrapper .el-link:hover {
    color: var(--primary-color-dark);
}

.link-wrapper .el-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-speed);
}

.link-wrapper .el-link:hover::after {
    width: 100%;
}

.link-wrapper .copy-btn {
    opacity: 0;
    transform: translateX(-10px);
    transition: all var(--transition-speed);
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 4px 12px;
    font-size: 12px;
    height: 24px;
    line-height: 1;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
}

.link-wrapper:hover .copy-btn {
    opacity: 1;
    transform: translateX(0);
}

.link-wrapper .copy-btn:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.el-table .el-table__cell {
    padding: 8px 0;
    text-align: left;
}

.el-table .el-table__header th {
    text-align: left;
    font-size: 14px;
    letter-spacing: 0.5px;
}

/* 优化表格内容溢出处理 */
.el-table .cell {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.main-container {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box;
}

.section-container {
    background: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 20px;
    overflow: hidden;
    min-height: auto;
}

.section-header {
    background: var(--primary-color-light);
    padding: 12px 20px;
    color: var(--primary-color);
    font-weight: bold;
    border-bottom: 1px solid var(--border-color);
}

.section-header h2 {
    margin: 0;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-content {
    padding: 15px;
}

.tool-section {
    margin-bottom: 20px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 16px;
    color: var(--text-color);
    font-weight: 600;
}

.section-title i {
    color: var(--primary-color);
    font-size: 18px;
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.tool-card {
    transition: all var(--transition-speed);
    border: none;
    background: var(--card-background);
    height: 100%;
    width: 100%;
    box-shadow: var(--shadow-sm);
}

.tool-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.tool-card-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 10px;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
}

.my-iframe{
    width: 100%;
    min-height: 800px;
}

.tool-icon {
    width: 28px;
    height: 28px;
    margin-bottom: 6px;
    object-fit: contain;
}

.tool-description {
    margin: 6px 0 0;
    font-size: 12px;
    color: var(--info-color);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.el-link {
    font-size: 13px !important;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.table-card {
    margin-top: 12px;
    border: none;
    background: var(--card-background);
}

.el-table {
    font-size: 13px;
}

.el-table th {
    padding: 8px 0 !important;
}

.el-table td {
    padding: 6px 0 !important;
}

.link-wrapper {
    padding: 2px 4px;
}

.link-wrapper .el-link {
    font-size: 13px;
}

.link-wrapper .copy-btn {
    height: 20px;
    line-height: 1;
    padding: 2px 8px;
    font-size: 12px;
}

@media (max-width: 768px) {
    .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .header-menu .el-menu-item {
        font-size: 13px;
        padding: 0 10px;
    }
    
    .header-menu .el-menu-item i {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .card-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .tool-card-content {
        padding: 8px;
    }
    
    .tool-icon {
        width: 24px;
        height: 24px;
    }
    
    .el-link {
        font-size: 12px !important;
    }
}

.tools-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.input-card, .preview-card, .decrypt-card, .doris-card, .task-card, .command-card, .log-card {
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    background: var(--card-background);
}

.input-group {
    margin-bottom: 20px;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-color);
    font-weight: 500;
}

.button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.button-group-vertical {
    display: flex;
    flex-direction: column;
    gap: 10px;
    justify-content: center;
    height: 100%;
    padding: 0 10px;
}

.preview-header {
    margin-bottom: 20px;
}

.preview-tip {
    color: var(--danger-color);
    font-size: 13px;
    margin-top: 10px;
}

.decrypt-header, .doris-header, .task-header, .command-header {
    margin-bottom: 20px;
}

.decrypt-tips, .doris-tips {
    margin-top: 15px;
}

.tip-item {
    color: var(--text-color-secondary);
    margin: 8px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.decrypt-content, .task-section {
    margin-top: 20px;
}

.input-section, .output-section {
    margin-bottom: 20px;
    width: 100%;
}

.input-section h4, .output-section h4 {
    margin-bottom: 10px;
    color: var(--text-color);
}

.task-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.command-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.function-tabs {
    margin-top: 20px;
}

.sm4-content {
    padding: 30px;
    width: 100%;
    box-sizing: border-box;
    background: var(--card-background);
    border-radius: var(--border-radius);
}

.sm4-content .el-row {
    width: 100%;
    margin: 0 !important;
    display: flex;
    align-items: stretch;
}

.sm4-content .el-col {
    padding: 0 !important;
}

.sm4-content .input-section,
.sm4-content .output-section {
    background: #f8faff;
    padding: 20px;
    border-radius: var(--border-radius);
    height: 100%;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-speed);
    min-height: 300px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
}

.sm4-content .input-section .el-textarea,
.sm4-content .output-section .el-textarea {
    flex: 1;
}

.sm4-content .input-section .el-textarea__inner,
.sm4-content .output-section .el-textarea__inner {
    height: 100% !important;
}

.sm4-content h4 {
    color: var(--text-color);
    font-size: 16px;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sm4-content h4::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 2px;
}

.sm4-content .button-group-vertical {
    display: flex;
    flex-direction: column;
    gap: 15px;
    justify-content: center;
    height: 100%;
    padding: 0 20px;
    align-items: center;
    min-height: 300px;
}

.sm4-content .button-group-vertical .el-button {
    width: 120px;
    height: 40px;
    font-size: 14px;
    border-radius: 20px;
    transition: all var(--transition-speed);
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sm4-content .button-group-vertical .el-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.sm4-content .external-link {
    margin-top: 30px;
    padding: 15px 20px;
    background: #f8faff;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all var(--transition-speed);
    justify-content: center;
    border: 1px solid var(--border-color);
}

.sm4-content .external-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    transition: all var(--transition-speed);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
}

.sm4-content .external-link a:hover {
    color: var(--primary-color-dark);
}

.sm4-content .external-link a i {
    font-size: 16px;
}

.sm4-content .el-textarea__inner {
    border-radius: var(--border-radius);
    border: 1px solid #dcdfe6;
    transition: all var(--transition-speed);
}

.sm4-content .el-textarea__inner:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(92,173,255,0.2);
}

@media (max-width: 768px) {
    .sm4-content {
        padding: 15px;
    }
    
    .sm4-content .button-group-vertical {
        flex-direction: row;
        padding: 15px 0;
    }
    
    .sm4-content .button-group-vertical .el-button {
        width: auto;
        flex: 1;
    }
}

/* 添加更好的响应式支持 */
@media (max-width: 1200px) {
    .header-content {
        padding: 0 30px;
        gap: 20px;
    }

    .header-menu .el-menu-item,
    .header-menu .el-submenu__title {
        padding: 0 18px;
        font-size: 13px;
        margin: 0 2px;
    }
}

@media (max-width: 768px) {
    .el-header {
        height: auto !important;
        line-height: normal;
    }

    .header-content {
        flex-direction: column;
        height: auto;
        padding: 15px;
        gap: 15px;
    }

    .header-left {
        justify-content: center;
    }

    .header-left h2 {
        font-size: 18px;
    }

    .header-menu {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .header-menu .el-menu-item,
    .header-menu .el-submenu__title {
        height: 50px;
        line-height: 50px;
        padding: 0 16px;
        font-size: 13px;
        margin: 2px;
    }

    .card-grid {
        grid-template-columns: 1fr;
    }

    .el-main {
        height: calc(100vh - 120px);
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 10px;
    }

    .header-left h2 {
        font-size: 18px;
    }

    .header-img {
        width: 40px;
        height: 26px;
    }

    .header-menu .el-menu-item,
    .header-menu .el-submenu__title {
        height: 45px;
        line-height: 45px;
        padding: 0 10px;
        font-size: 12px;
    }
}